import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Star, Shield, Truck, RotateCcw, Check, ArrowRight } from "lucide-react";
const heroImage = "/lovable-uploads/55d0b835-d5e0-4079-92bf-60e8ac6d8982.png";

const Index = () => {
  const [email, setEmail] = useState("");
  const [selectedColor, setSelectedColor] = useState("gray");

  const productColors = [
    { id: "gray", name: "Gr<PERSON>", color: "bg-slate-400", price: "549" },
    { id: "navy", name: "Navy", color: "bg-slate-700", price: "549" },
    { id: "pink", name: "<PERSON>", color: "bg-pink-300", price: "549" },
    { id: "black", name: "Sort", color: "bg-slate-900", price: "549" }
  ];

  const features = [
    { icon: <Shield className="w-6 h-6" />, title: "360° støtte", desc: "Støtter både nakke og kinn for maksimal komfort" },
    { icon: <Star className="w-6 h-6" />, title: "Memory foam", desc: "Tilpasser seg din kroppsform perfekt" },
    { icon: <ArrowRight className="w-6 h-6" />, title: "Kompakt design", desc: "Tar minimal plass i håndbagasjen" },
  ];

  const benefits = [
    "Bedre søvnkvalitet på reiser",
    "Ergonomisk design reduserer nakkesmerter", 
    "Justerbar og kompakt",
    "Stilrent design i flere farger",
    "Leveres med oppbevaringsbag"
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav className="sticky top-0 z-50 bg-white/95 backdrop-blur-lg border-b border-border shadow-soft">
        <div className="container mx-auto px-4 py-2.5 flex justify-between items-center">
          <div className="flex items-center">
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              viewBox="117.94999999999999 178.02 340.6499984741211 218.96026611328122"
              className="h-32 w-auto"
            >
              <svg xmlns="http://www.w3.org/2000/svg" x="187.95" y="248.02" viewBox="205.5 85 430.9000244140625 425.29998779296875" height="78.96026611328125" width="80" className="overflow-visible">
                <path className="fill-primary" d="M233,195.2v315.1h-27.5V214.1L233,195.2z M608.9,195.2l27.5,18.9v296.2h-27.5V195.2z M555.7,158.9v351.3h-27.5V140.2L555.7,158.9z M475,103.8v406.5h-27.5V85L475,103.8z M286.2,158.9l27.5-18.8v370.1h-27.5V158.9z M366.9,103.8L394.4,85v425.3h-27.5V103.8z" style={{fillRule: 'evenodd', clipRule: 'evenodd'}}></path>
              </svg>
              <svg x="287.5" y="307.34" viewBox="1.0299999713897705 -0.07000000774860382 101.0999984741211 19.639999389648438" height="19.639999389648438" width="101.0999984741211" className="overflow-visible">
                <g className="fill-primary">
                  <path d="M1.03 0.68L3.90 0.68L9.85 15.63L8.47 15.35L14.13 0.68L16.95 0.68L8.97 19.57L1.03 0.68ZM23.08 18.75L23.08 0.68L25.50 0.68L25.50 18.75L23.08 18.75ZM31.88 3L31.88 0.68L43.88 0.68L43.88 3L39.03 3L39.03 18.75L36.60 18.75L36.60 3L31.88 3ZM50.23 18.75L50.23 0.68L61.98 0.68L61.98 3L52.65 3L52.65 8.50L60.98 8.50L60.98 10.82L52.65 10.82L52.65 16.43L62.33 16.43L62.33 18.75L50.23 18.75ZM69.02 0.68L73.83 0.68Q75.20 0.68 76.36 1.04Q77.53 1.40 78.39 2.11Q79.25 2.82 79.74 3.86Q80.22 4.90 80.22 6.27L80.22 6.27Q80.22 7.35 79.91 8.38Q79.60 9.40 78.88 10.24Q78.15 11.07 76.99 11.59Q75.83 12.10 74.13 12.10L74.13 12.10L71.45 12.10L71.45 18.75L69.02 18.75L69.02 0.68ZM71.45 9.78L74.08 9.78Q75.15 9.78 75.86 9.45Q76.58 9.13 76.97 8.61Q77.38 8.10 77.55 7.50Q77.72 6.90 77.72 6.38L77.72 6.38Q77.72 5.82 77.54 5.24Q77.35 4.65 76.94 4.15Q76.53 3.65 75.86 3.32Q75.20 3 74.25 3L74.25 3L71.45 3L71.45 9.78ZM74.17 11.22L77.05 11.15L81.78 18.75L78.97 18.75L74.17 11.22ZM88.58 18.75L86.20 18.75L94.10-0.07L94.25-0.07L102.13 18.75L99.35 18.75L93.45 3.72L95.18 2.60L88.58 18.75ZM90.13 14L90.83 11.90L97.55 11.90L98.35 14L90.13 14Z"></path>
                </g>
              </svg>
            </svg>
          </div>
          <div className="hidden md:flex space-x-8">
            <a href="#hjem" className="text-muted-foreground hover:text-foreground transition-colors font-medium">Hjem</a>
            <a href="#produkt" className="text-muted-foreground hover:text-foreground transition-colors font-medium">Produkt</a>
            <a href="#om-oss" className="text-muted-foreground hover:text-foreground transition-colors font-medium">Om Oss</a>
            <a href="#kontakt" className="text-muted-foreground hover:text-foreground transition-colors font-medium">Kontakt oss</a>
            <a href="#faq" className="text-muted-foreground hover:text-foreground transition-colors font-medium">Ofte stilte spørsmål</a>
          </div>
          <Button variant="outline">Kontakt</Button>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-soft overflow-hidden">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <Badge variant="secondary" className="w-fit">
                Over 2000 fornøyde kunder ⭐
              </Badge>
              
              <div className="space-y-4">
                <h1 className="text-5xl lg:text-6xl font-bold text-foreground leading-tight">
                  Søvn på reiser som faktisk <span className="text-accent">fungerer</span>
                </h1>
                <p className="text-xl text-muted-foreground max-w-lg">
                  Etter å ha testet alt på markedet, fant vi endelig en nakkepute som holder deg komfortabel hele reisen.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button variant="premium" size="lg" className="text-lg px-8 py-6">
                  Kjøp Vitera nakkepute
                  <ArrowRight className="ml-2" />
                </Button>
                <Button variant="outline" size="lg" className="text-lg px-8 py-6">
                  Se produktvideo
                </Button>
              </div>

              <div className="flex items-center gap-8 pt-4">
                <div className="flex items-center gap-2">
                  <Truck className="w-5 h-5 text-success" />
                  <span className="text-sm text-muted-foreground">Gratis frakt</span>
                </div>
                <div className="flex items-center gap-2">
                  <RotateCcw className="w-5 h-5 text-success" />
                  <span className="text-sm text-muted-foreground">30 dagers retur</span>
                </div>
                <div className="flex items-center gap-2">
                  <Shield className="w-5 h-5 text-success" />
                  <span className="text-sm text-muted-foreground">100% garanti</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="relative z-10">
                <img 
                  src={heroImage} 
                  alt="Vitera nakkepute - ergonomisk reisepute med memory foam" 
                  className="w-full max-w-lg mx-auto rounded-3xl shadow-strong"
                />
              </div>
              <div className="absolute -top-8 -right-8 w-32 h-32 bg-gradient-accent rounded-full blur-3xl opacity-20"></div>
              <div className="absolute -bottom-8 -left-8 w-40 h-40 bg-accent/20 rounded-full blur-3xl"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="fordeler" className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">
              Det kunder sier om forskjellen
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Vi spurte 500 reisende hva de savnet mest. Her er hva som gjorde forskjellen.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="border-0 shadow-medium hover:shadow-strong transition-shadow">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gradient-accent rounded-2xl flex items-center justify-center mx-auto mb-6 text-white">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-foreground mb-3">{feature.title}</h3>
                  <p className="text-muted-foreground">{feature.desc}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Product Showcase */}
      <section id="produkt" className="py-20 bg-gradient-soft">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-foreground mb-4">
              Velg din favorittfarge
            </h2>
            <p className="text-xl text-muted-foreground">
              Tilgjengelig i fire stilrene farger som passer enhver reisestil
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="grid md:grid-cols-4 gap-6 mb-12">
              {productColors.map((color) => (
                <Card 
                  key={color.id}
                  className={`cursor-pointer transition-all hover:shadow-medium ${
                    selectedColor === color.id ? 'ring-2 ring-accent shadow-medium' : ''
                  }`}
                  onClick={() => setSelectedColor(color.id)}
                >
                  <CardContent className="p-6 text-center">
                    <div className={`w-16 h-16 ${color.color} rounded-full mx-auto mb-4 shadow-medium`}></div>
                    <h3 className="font-semibold text-foreground">{color.name}</h3>
                    <p className="text-accent font-bold text-lg">{color.price} NOK</p>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="text-center">
              <Button variant="premium" size="lg" className="text-lg px-12 py-6">
                Legg i handlekurv - {productColors.find(c => c.id === selectedColor)?.price} NOK
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold text-foreground mb-8">
                Hvorfor velge vår nakkepute?
              </h2>
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-success rounded-full flex items-center justify-center flex-shrink-0">
                      <Check className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-foreground">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="bg-gradient-accent rounded-3xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">Hva gjorde forskjellen for oss:</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span>Støtter kinn og nakke samtidig</span>
                  <span className="font-bold">Endelig!</span>
                </div>
                <div className="flex justify-between">
                  <span>Holder formen time etter time</span>
                  <span className="font-bold">Memory foam</span>
                </div>
                <div className="flex justify-between">
                  <span>Passer i håndbagasjen</span>
                  <span className="font-bold">Kompakt</span>
                </div>
                <div className="flex justify-between">
                  <span>Ser ikke ut som medisinsk utstyr</span>
                  <span className="font-bold">Elegant</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-20 bg-gradient-soft">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-2xl mx-auto">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Meld deg på nyhetsbrevet
            </h2>
            <p className="text-muted-foreground mb-8">
              Få eksklusive tilbud og reisetips direkt i innboksen din
            </p>
            <div className="flex gap-4 max-w-md mx-auto">
              <Input 
                placeholder="Din e-postadresse"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="flex-1"
              />
              <Button variant="premium">
                Meld meg på
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-primary text-primary-foreground py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <div className="text-2xl font-bold mb-4">Vitera</div>
              <p className="text-primary-foreground/80 mb-4">
                Norges ledende leverandør av ergonomiske reiseprodukter. Vi skaper komfort for den moderne reisende.
              </p>
              <div className="space-y-2 text-sm text-primary-foreground/80">
                <p>📞 +47 123 45 678</p>
                <p>✉️ <EMAIL></p>
                <p>🏢 Storgata 1, 0155 Oslo</p>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-8">
              <div>
                <h4 className="font-semibold mb-3">Kundeservice</h4>
                <div className="space-y-2 text-sm text-primary-foreground/80">
                  <p>Om oss</p>
                  <p>Kontakt</p>
                  <p>Frakt og levering</p>
                  <p>Returpolicy</p>
                </div>
              </div>
              <div>
                <h4 className="font-semibold mb-3">Informasjon</h4>
                <div className="space-y-2 text-sm text-primary-foreground/80">
                  <p>Personvern</p>
                  <p>Vilkår og betingelser</p>
                  <p>Cookie-policy</p>
                  <p>FAQ</p>
                </div>
              </div>
            </div>
          </div>
          <div className="border-t border-primary-foreground/20 mt-8 pt-8 text-center text-sm text-primary-foreground/60">
            <p>© 2024 Vitera. Alle rettigheter reservert.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;